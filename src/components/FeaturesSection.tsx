'use client';

import { useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { useTranslations } from 'next-intl';
import gradientGL from 'gradient-gl';

const FeaturesSection = () => {
  const features = useTranslations('features');

  useEffect(() => {
    // Initialize gradient-gl for features section
    gradientGL('b5.fc04', '#features-gradient-bg');
  }, []);

  // Intersection observers for staggered animations
  const { ref: backgroundRef, inView: backgroundInView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  const { ref: titleRef, inView: titleInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: feature1Ref, inView: feature1InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: feature2Ref, inView: feature2InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: feature3Ref, inView: feature3InView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const { ref: benefitsRef, inView: benefitsInView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  return (
    <section ref={backgroundRef} className="py-32 relative overflow-hidden">
      {/* WebGL Gradient Background */}
      <div
        id="features-gradient-bg"
        className={`gradient-container absolute inset-0 z-0 transition-opacity duration-1500 ease-out delay-1500 ${
          backgroundInView ? 'opacity-100' : 'opacity-0'
        }`}
      ></div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 z-10">
        <div className="flex flex-col md:flex-row">
          {/* Left 2/3 - Title only */}
          <div className="md:w-2/3 mb-16 md:mb-0">
            {/* Main Title */}
            <div
              ref={titleRef}
              className={`transition-all duration-1000 ease-out delay-300 ${
                titleInView
                  ? 'opacity-100 translate-y-0'
                  : 'opacity-0 translate-y-8'
              }`}
            >
              <h2 className="text-4xl lg:text-7xl font-normal text-albatros-ivory">
                Djelatnosti OD Albatros proširuju se shodno Vašim administrativnim potrebama.
              </h2>
            </div>
          </div>

          {/* Right 1/3 - Features List */}
          <div className="md:w-1/3">
            <div className="flex flex-col text-xl text-albatros-ivory border-t border-white/20">
              <p
                ref={feature1Ref}
                className={`py-3 border-b border-white/20 transition-all duration-1000 ease-out delay-500 ${
                  feature1InView
                    ? 'opacity-100 translate-x-0'
                    : 'opacity-0 translate-x-8'
                }`}
              >
                {features('fast')}
              </p>
              <p
                ref={feature2Ref}
                className={`py-3 border-b border-white/20 transition-all duration-1000 ease-out delay-700 ${
                  feature2InView
                    ? 'opacity-100 translate-x-0'
                    : 'opacity-0 translate-x-8'
                }`}
              >
                {features('reliable')}
              </p>
              <p
                ref={feature3Ref}
                className={`py-3 border-b border-white/20 transition-all duration-1000 ease-out delay-900 ${
                  feature3InView
                    ? 'opacity-100 translate-x-0'
                    : 'opacity-0 translate-x-8'
                }`}
              >
                {features('discrete')}
              </p>
            </div>
          </div>
        </div>

        {/* Full width Important Notes section */}
        <div
          ref={benefitsRef}
          className={`mt-16 pt-8 border-t border-white/20 transition-all duration-1000 ease-out delay-1100 ${
            benefitsInView
              ? 'opacity-100 translate-y-0'
              : 'opacity-0 translate-y-8'
          }`}
        >
          <h3 className="text-lg text-albatros-ivory mb-4 leading-tight">Napomena:</h3>
          <div className="flex flex-col font-semibold text-sm text-albatros-ivory/90">
            <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Vremenski rok pribavljanja dokumenata zavisi od vrste dokumenata i vremena potrebnog institucijama.</span>
            <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Za obavljanje naših djelatnosti potrebna nam je Vaša ovjerena punomoć.</span>
            <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Građani BiH u BiH punomoć vade i ovjeravaju u općini.</span>
            <span className="mb-2 before:content-['•'] before:text-albatros-ivory/60 before:mr-1.5">Državljani Bosne i Hercegovine u inostranstvu punomoć mogu ovjeriti u konzulatu.</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
